<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <LangVersion>latest</LangVersion>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <AssemblyName>Sanet.MakaMek.Avalonia</AssemblyName>
        <RootNamespace>Sanet.MakaMek.Avalonia</RootNamespace>
        <Description>Avalonia UI frontend for MakaMek game</Description>
    </PropertyGroup>

    <ItemGroup>
        <None Include="../../../README.md" Pack="true" PackagePath="" />
    </ItemGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**"/>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="Resources\**\*.mtf" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.3.5" />
        <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.5" />
        <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.5" />
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.3.5" />
        <PackageReference Include="Avalonia.Xaml.Behaviors" Version="********" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.9" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.9" />
        <PackageReference Include="Sanet.MVVM.DI.Avalonia" Version="1.1.1.1113" />
        <PackageReference Include="Sanet.MVVM.Navigation.Avalonia" Version="1.1.1.1113" />
        <PackageReference Include="Sanet.MVVM.Views.Avalonia" Version="1.1.1.1113" />
        <PackageReference Include="System.Reactive" Version="6.0.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\MakaMek.Core\MakaMek.Core.csproj" />
      <ProjectReference Include="..\..\MakaMek.Presentation\MakaMek.Presentation.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Views\NewGame\NewGameViewNarrow.axaml.cs">
        <SubType>Code</SubType>
      </Compile>
      <Compile Update="Views\NewGame\NewGameViewWide.axaml.cs">
        <SubType>Code</SubType>
      </Compile>
    </ItemGroup>

    <!-- Debug target to print properties -->
    <Target Name="PrintProperties" BeforeTargets="Build">
        <Message Importance="high" Text="Project: $(MSBuildProjectName)" />
        <Message Importance="high" Text="Version: $(Version)" />
        <Message Importance="high" Text="Authors: <AUTHORS>
        <Message Importance="high" Text="Company: $(Company)" />
    </Target>
</Project>
